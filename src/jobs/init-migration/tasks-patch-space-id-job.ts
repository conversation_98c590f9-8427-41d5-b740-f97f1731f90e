import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { ClickUpClient } from '@/data/clickup-client';
import { ClickUpTask } from '@/data/types/clickup.types';

// Minimal task data to reduce memory usage
type TaskSpaceData = {
  id: string;
  space_id: string;
};

export const tasksPatchSpaceIdJob = client.defineJob({
  id: 'tasks-patch-space-id-job',
  name: 'Tasks Patch Space ID Job',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, _) => {
    const clickupClient = new ClickUpClient();

    // 1. FETCH ALL SPACES
    const spaceIds = await io.runTask('fetch-all-clickup-spaces', async () => {
      const spacesResponse = await clickupClient.getSpaces();

      if ('err' in spacesResponse) {
        throw new Error(`Error fetching spaces: ${JSON.stringify(spacesResponse.err)}`);
      }

      return spacesResponse.spaces.map((space) => space.id);
    });

    await io.logger.info(`Found ${spaceIds.length} spaces to process`);

    // 2. FETCH ALL TASKS FROM ALL SPACES AND COLLECT MINIMAL DATA
    let allTasksWithSpaceId: TaskSpaceData[] = [];

    for (const spaceId of spaceIds) {
      const minTasksFromSpace = await io.runTask(`fetch-tasks-from-space-${spaceId}`, async () => {
        let page = 0;
        let lastPage = false;
        let tasksFromSpace = 0;
        let minTasks: TaskSpaceData[] = [];

        while (!lastPage) {
          const [minimalTasks, isLastPage] = await io.runTask(`fetch-space-${spaceId}-page-${page}`, async () => {
            const tasksResponse = await clickupClient.getTasksOfSpaces(page, [spaceId]);

            if ('err' in tasksResponse) {
              throw new Error(
                `Error fetching tasks from space ${spaceId}: ${JSON.stringify(tasksResponse)} at page ${page}`,
              );
            }

            // Extract only the minimal data we need immediately
            const minimalTasks: TaskSpaceData[] = tasksResponse.tasks.map((task: ClickUpTask) => ({
              id: task.id,
              space_id: spaceId,
            }));

            return [minimalTasks, tasksResponse.last_page];
          });

          minTasks = minTasks.concat(minimalTasks);
          tasksFromSpace += minimalTasks.length;

          await io.logger.info(`Fetched ${minimalTasks.length} tasks from space ${spaceId}, page ${page}`);

          lastPage = isLastPage;
          page++;
        }

        await io.logger.info(`Total tasks from space ${spaceId}: ${tasksFromSpace}`);
        return minTasks;
      });

      allTasksWithSpaceId = allTasksWithSpaceId.concat(minTasksFromSpace);
    }

    await io.logger.info(`Total tasks fetched from ClickUp: ${allTasksWithSpaceId.length}`);

    // 3. UPDATE ALL TASKS IN DATABASE WITH SINGLE UPSERT
    await io.runTask('bulk-upsert-tasks-with-space-id', async () => {
      const supabase = createClient();

      // Prepare data for bulk upsert
      const upsertData = allTasksWithSpaceId.map((taskData) => ({
        clickup_task_id: taskData.id,
        clickup_space_id: Number(taskData.space_id),
      }));

      const { error } = await supabase.from('tasks').upsert(upsertData, {
        onConflict: 'clickup_task_id',
      });

      if (error) {
        throw new Error(`Error bulk updating tasks with space IDs: ${JSON.stringify(error)}`);
      }

      await io.logger.info(`Processing complete. Bulk upserted ${allTasksWithSpaceId.length} tasks with space IDs`);
    });
  },
});
