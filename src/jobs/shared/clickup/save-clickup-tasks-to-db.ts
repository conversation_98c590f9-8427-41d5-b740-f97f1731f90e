import { createClient } from '@/data/supabase-server';
import { ClickUpTask } from '@/data/types/clickup.types';

export async function saveClickupTasksToDb(clientTasks: ClickUpTask[], nonClientTasks: ClickUpTask[]) {
  const tasksUpsertClient = clientTasks.map((task) => {
    const userId = task.assignees[0]?.id && task.assignees[0]?.id > 0 ? task.assignees[0]?.id : task.creator?.id;
    return {
      clickup_task_id: task.id,
      name: task.name,
      clickup_list_id: task.list.id,
      clickup_due_date: Number(task.due_date),
      clickup_task_description: task.description,
      clickup_time_estimate: task.time_estimate,
      clickup_user_id: userId < 0 ? null : String(userId),
      clickup_url: task.url,
      is_client_task: true,
      clickup_status: task.status.type === 'closed' ? 'Closed' : task.status.status,
      clickup_date_closed: task.date_closed ? Number(task.date_closed) : null,
      clickup_space_id: task.space?.id ? Number(task.space.id) : null,
    };
  });

  const tasksUpsert = nonClientTasks
    .map((task) => {
      const userId = task.assignees[0]?.id && task.assignees[0]?.id > 0 ? task.assignees[0]?.id : task.creator?.id;
      return {
        clickup_task_id: task.id,
        name: task.name,
        clickup_list_id: task.list.id,
        clickup_due_date: Number(task.due_date),
        clickup_task_description: task.description,
        clickup_time_estimate: task.time_estimate,
        clickup_user_id: userId < 0 ? null : String(userId),
        clickup_url: task.url,
        is_client_task: false,
        clickup_status: task.status.type === 'closed' ? 'Closed' : task.status.status,
        clickup_date_closed: task.date_closed ? Number(task.date_closed) : null,
        clickup_space_id: task.space?.id ? Number(task.space.id) : null,
      };
    })
    .concat(tasksUpsertClient);

  const uniqueTasks = tasksUpsert.filter(
    (task, index, self) => self.findIndex((t) => t.clickup_task_id === task.clickup_task_id) === index,
  );

  const supabase = createClient();
  const { error } = await supabase.from('tasks').upsert(uniqueTasks, { onConflict: 'clickup_task_id' });
  if (error) {
    throw new Error(`Error upserting tasks: ${JSON.stringify(error)}`);
  }
}
